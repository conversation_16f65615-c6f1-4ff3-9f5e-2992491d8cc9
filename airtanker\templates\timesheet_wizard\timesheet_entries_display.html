{% extends "base.html" %}

{% block styles %}
<link href="https://cdn.datatables.net/buttons/3.1.2/css/buttons.bootstrap5.css" rel="stylesheet">
<link href="https://cdn.datatables.net/rowgroup/1.5.0/css/rowGroup.bootstrap5.css" rel="stylesheet">
<style>
    .stats-card {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-align: center;
        border: 1px solid #e9ecef;
    }
    
    .stats-card h3 {
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #2c3e50;
    }
    
    .stats-card p {
        color: #6c757d;
        margin: 0;
        font-size: 0.9rem;
    }
    
    .stats-card .icon {
        font-size: 1.2rem;
        color: #6c757d;
        margin-left: 0.5rem;
    }
    
    .filters-section {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        border: 1px solid #e9ecef;
    }
    
    .entries-section {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
    }
    
    .status-valid {
        background-color: #d4edda;
        color: #155724;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .status-error {
        background-color: #f8d7da;
        color: #721c24;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .status-skipping {
        background: repeating-linear-gradient(
            45deg,
            #fff3cd,
            #fff3cd 3px,
            #ffeaa7 3px,
            #ffeaa7 6px
        );
        color: #856404;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        border: 1px solid #ffeaa7;
    }

    .status-valid:hover,
    .status-error:hover,
    .status-skipping:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
    
    .rate-type-reg {
        background-color: #d1ecf1;
        color: #0c5460;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .rate-type-ot {
        background-color: #fff3cd;
        color: #856404;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .rate-type-dt{
        background-color: #c6e48b;
        color: #0f5132;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .rate-type-trt{
        background-color: #b3d9ff;
        color: #004085;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .rate-type-unknown{
        background-color: #f8d7da;
        color: #721c24;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .employee-name-dimmed {
        color: #6c757d;
        font-style: italic;
    }

    .employee-dropdown {
        font-size: 0.85rem;
        padding: 0.25rem 0.5rem;
        border: 1px solid #ced4da;
        border-radius: 4px;
        background-color: #fff;
        min-width: 150px;
    }

    .employee-dropdown:focus {
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .workorder-name-dimmed {
        color: #6c757d;
        font-style: italic;
    }

    .workorder-dropdown {
        font-size: 0.85rem;
        padding: 0.25rem 0.5rem;
        border: 1px solid #ced4da;
        border-radius: 4px;
        background-color: #fff;
        min-width: 200px;
    }

    .workorder-dropdown:focus {
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    .export-btn {
        background-color: #28a745;
        border-color: #28a745;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .export-btn:hover {
        background-color: #218838;
        border-color: #1e7e34;
        color: white;
        text-decoration: none;
    }

    .export-buttons {
        display: flex;
        gap: 0.5rem;
    }
    
    .export-button {
        padding: 0.25rem 0.5rem;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .export-button:hover {
        transform: scale(1.5);
    }
    
    .export-button svg {
        width: 16px;
        height: 16px;
        fill: currentColor;
    }
    
    .table-container {
        overflow-x: auto;
        margin-top: 1rem;
    }
    
    #entriesTable {
        font-size: 0.9rem;
    }
    
    #entriesTable th {
        background-color: #f8f9fa;
        border-top: none;
        font-weight: 600;
        color: #495057;
        white-space: nowrap;
    }
    
    #entriesTable td {
        vertical-align: middle;
        white-space: nowrap;
    }
    
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }
    
    .page-title {
        font-size: 1.75rem;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
    }
    
    .page-subtitle {
        color: #6c757d;
        font-size: 0.9rem;
        margin: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div>
            <h1 class="page-title">Timesheet Entries</h1>
            <p class="page-subtitle">Parsed customer timesheet data{% if week_ending %} - Week Ending: {{ week_ending }}{% endif %}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ url_for('upload_customer_timesheets_ai') }}" class="btn btn-outline-secondary d-flex align-items-center">
                <i class="ri-arrow-left-line align-middle me-2"></i>
                Back to Upload
            </a>
            <a href="#" class="btn export-btn d-flex align-items-center" onclick="exportData()">
                <i class="ri-download-line align-middle me-1"></i>
                Export Data
            </a>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col">
            <div class="stats-card">
                <h3 id="totalHours">0</h3>
                <p>Total Hours <i class="ri-time-line icon align-middle"></i></p>
            </div>
        </div>
        <div class="col">
            <div class="stats-card">
                <h3 id="totalEntries">0</h3>
                <p>Total Entries <i class="ri-file-list-line icon align-middle"></i></p>
            </div>
        </div>
        <div class="col">
            <div class="stats-card">
                <h3 id="totalEmployees">0</h3>
                <p>Employees <i class="ri-user-line icon align-middle"></i></p>
            </div>
        </div>
        <div class="col">
            <div class="stats-card">
                <h3 id="totalCustomers">0</h3>
                <p>Customers <i class="ri-building-line icon align-middle"></i></p>
            </div>
        </div>
        <div class="col">
            <div class="stats-card">
                <h3 id="totalErrors">0</h3>
                <p>Errors <i class="ri-error-warning-line icon align-middle"></i></p>
            </div>
        </div>
        <div class="col">
            <div class="stats-card">
                <h3 id="totalSkipped">0</h3>
                <p>Skipped <i class="ri-skip-forward-line icon align-middle"></i></p>
            </div>
        </div>
    </div>
    
    <!-- Filters & Controls -->
    <div class="filters-section">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-3">Filters & Controls</h5>
            <!-- Export Buttons -->
            <div class="export-buttons mb-3">
                <div class="export-button" id="copyButton" title="Copy to clipboard">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-clipboard" viewBox="0 0 16 16">
                        <path d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1z"/>
                        <path d="M9.5 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5zm-3-1A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0z"/>
                    </svg>
                </div>
                <div class="export-button" id="csvButton" title="Export to CSV">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-filetype-csv" viewBox="0 0 16 16">
                        <path fill-rule="evenodd" d="M14 4.5V14a2 2 0 0 1-2 2h-1v-1h1a1 1 0 0 0 1-1V4.5h-2A1.5 1.5 0 0 1 9.5 3V1H4a1 1 0 0 0-1 1v9H2V2a2 2 0 0 1 2-2h5.5zM3.517 14.841a1.13 1.13 0 0 0 .401.823q.195.162.478.252.284.091.665.091.507 0 .859-.158.354-.158.539-.44.187-.284.187-.656 0-.336-.134-.56a1 1 0 0 0-.375-.357 2 2 0 0 0-.566-.21l-.621-.144a1 1 0 0 1-.404-.176.37.37 0 0 1-.144-.299q0-.234.185-.384.188-.152.512-.152.214 0 .37.068a.6.6 0 0 1 .246.181.56.56 0 0 1 .12.258h.75a1.1 1.1 0 0 0-.2-.566 1.2 1.2 0 0 0-.5-.41 1.8 1.8 0 0 0-.78-.152q-.439 0-.776.15-.337.149-.527.421-.19.273-.19.639 0 .302.122.524.124.223.352.367.228.143.539.213l.618.144q.31.073.463.193a.39.39 0 0 1 .152.326.5.5 0 0 1-.085.29.56.56 0 0 1-.255.193q-.167.07-.413.07-.175 0-.32-.04a.8.8 0 0 1-.248-.115.58.58 0 0 1-.255-.384zM.806 13.693q0-.373.102-.633a.87.87 0 0 1 .302-.399.8.8 0 0 1 .475-.137q.225 0 .398.097a.7.7 0 0 1 .272.26.85.85 0 0 1 .12.381h.765v-.072a1.33 1.33 0 0 0-.466-.964 1.4 1.4 0 0 0-.489-.272 1.8 1.8 0 0 0-.606-.097q-.534 0-.911.223-.375.222-.572.632-.195.41-.196.979v.498q0 .568.193.976.197.407.572.626.375.217.914.217.439 0 .785-.164t.55-.454a1.27 1.27 0 0 0 .226-.674v-.076h-.764a.8.8 0 0 1-.118.363.7.7 0 0 1-.272.25.9.9 0 0 1-.401.087.85.85 0 0 1-.478-.132.83.83 0 0 1-.299-.392 1.7 1.7 0 0 1-.102-.627zm8.239 2.238h-.953l-1.338-3.999h.917l.896 3.138h.038l.888-3.138h.879z"/>
                    </svg>
                </div>
                <div class="export-button" id="excelButton" title="Export to Excel">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-file-earmark-spreadsheet" viewBox="0 0 16 16">
                        <path d="M14 14V4.5L9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2M9.5 3A1.5 1.5 0 0 0 11 4.5h2V9H3V2a1 1 0 0 1 1-1h5.5zM3 12v-2h2v2zm0 1h2v2H4a1 1 0 0 1-1-1zm3 2v-2h3v2zm4 0v-2h3v1a1 1 0 0 1-1 1zm3-3h-3v-2h3zm-7 0v-2h3v2z"/>
                    </svg>
                </div>
                <div class="export-button" id="pdfButton" title="Export to PDF">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-file-earmark-pdf" viewBox="0 0 16 16">
                        <path d="M14 14V4.5L9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2M9.5 3A1.5 1.5 0 0 0 11 4.5h2V14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h5.5z"/>
                        <path d="M4.603 14.087a.8.8 0 0 1-.438-.42c-.195-.388-.13-.776.08-1.102.198-.307.526-.568.897-.787a7.7 7.7 0 0 1 1.482-.645 20 20 0 0 0 1.062-2.227 7.3 7.3 0 0 1-.43-1.295c-.086-.4-.119-.796-.046-1.136.075-.354.274-.672.65-.823.192-.077.4-.12.602-.077a.7.7 0 0 1 .477.365c.088.164.12.356.127.538.007.188-.012.396-.047.614-.084.51-.27 1.134-.52 1.794a11 11 0 0 0 .98 1.686 5.8 5.8 0 0 1 1.334.05c.364.066.734.195.96.465.12.144.193.32.2.518.007.192-.047.382-.138.563a1.04 1.04 0 0 1-.354.416.86.86 0 0 1-.51.138c-.331-.014-.654-.196-.933-.417a5.7 5.7 0 0 1-.911-.95 11.7 11.7 0 0 0-1.997.406 11.3 11.3 0 0 1-1.02 1.51c-.292.35-.609.656-.927.787a.8.8 0 0 1-.58.029m1.379-1.901q-.25.115-.459.238c-.328.194-.541.383-.647.547-.094.145-.096.25-.04.361q.016.032.026.044l.035-.012c.137-.056.355-.235.635-.572a8 8 0 0 0 .45-.606m1.64-1.33a13 13 0 0 1 1.01-.193 12 12 0 0 1-.51-.858 21 21 0 0 1-.5 1.05zm2.446.45q.226.245.435.41c.24.19.407.253.498.256a.1.1 0 0 0 .07-.015.3.3 0 0 0 .094-.125.44.44 0 0 0 .059-.2.1.1 0 0 0-.026-.063c-.052-.062-.2-.152-.518-.209a4 4 0 0 0-.612-.053zM8.078 7.8a7 7 0 0 0 .2-.828q.046-.282.038-.465a.6.6 0 0 0-.032-.198.5.5 0 0 0-.145.04c-.087.035-.158.106-.196.283-.04.192-.03.469.046.822q.036.167.09.346z"/>
                    </svg>
                </div>
                <div class="export-button" id="printButton" title="Print">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-printer" viewBox="0 0 16 16">
                        <path d="M2.5 8a.5.5 0 1 0 0-1 .5.5 0 0 0 0 1"/>
                        <path d="M5 1a2 2 0 0 0-2 2v2H2a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h1v1a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2v-1h1a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2h-1V3a2 2 0 0 0-2-2zM4 3a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1v2H4zm1 5a2 2 0 0 0-2 2v1H2a1 1 0 0 1-1-1V7a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-1v-1a2 2 0 0 0-2-2zm7 2v3a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1v-3a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1"/>
                    </svg>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col">
                <label for="searchInput" class="form-label">Search</label>
                <input type="text" class="form-control" id="searchInput" placeholder="Search entries...">
            </div>
            <div class="col-md-2">
                <label for="groupBySelect" class="form-label">Group By</label>
                <select class="form-select" id="groupBySelect">
                    <option value="">No Grouping</option>
                    <option value="CustomerName">Customer</option>
                    <option value="EmployeeName">Employee</option>
                    <option value="ProjectID">Project</option>
                    <option value="Date">Date</option>
                    <option value="RateTypeID">Rate Type</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="sortBySelect" class="form-label">Sort By</label>
                <select class="form-select" id="sortBySelect">
                    <option value="Date">Date</option>
                    <option value="CustomerReportedHours">Hours</option>
                    <option value="CustomerName">Customer</option>
                    <option value="EmployeeName">Employee</option>
                    <option value="ProjectID">Project</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="sortOrderSelect" class="form-label">Sort Order</label>
                <select class="form-select" id="sortOrderSelect">
                    <option value="asc">Ascending</option>
                    <option value="desc">Descending</option>
                </select>
            </div>
        </div>
    </div>
    
    <!-- Entries Table -->
    <div class="entries-section">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">
                <i class="ri-file-list-3-line"></i>
                All Entries
                <span id="entriesCount" class="text-muted">0 entries</span>
            </h5>            
            <div class="text-muted">
                Total: <span id="totalHoursDisplay">0</span> hours
            </div>
        </div>

        <div class="table-container">
            {% if entries and entries|length > 0 %}
            <table id="entriesTable" class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Employee</th>
                        <th>Customer</th>
                        <th>Project</th>
                        <th>Work Order</th>
                        <th>Hours</th>
                        <th>Rate Type</th>
                        <th>File</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be populated by JavaScript -->
                </tbody>
            </table>
            {% else %}
            <div class="alert alert-info text-center">
                <i class="ri-information-line"></i>
                No timesheet entries found. Please upload and process timesheet files first.
                <br><br>
                <a href="{{ url_for('upload_customer_timesheets_ai') }}" class="btn btn-primary">
                    <i class="ri-upload-line"></i>
                    Upload Timesheets
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.datatables.net/2.1.8/js/dataTables.js"></script>
<script src="https://cdn.datatables.net/2.1.8/js/dataTables.bootstrap5.js"></script>
<script src="https://cdn.datatables.net/buttons/3.1.2/js/dataTables.buttons.js"></script>
<script src="https://cdn.datatables.net/buttons/3.1.2/js/buttons.bootstrap5.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/3.1.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/3.1.2/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/rowgroup/1.5.0/js/dataTables.rowGroup.js"></script>
<script>
let entriesData = {{ entries|tojson }};
let dataTable;

$(document).ready(function() {
    // Handle case where entries might be null or undefined
    if (!entriesData || !Array.isArray(entriesData)) {
        entriesData = [];
    }

    // Only initialize table if we have data and the table element exists
    if (entriesData.length > 0 && $('#entriesTable').length > 0) {
        initializeTable();
        setupEventListeners();
    }

    updateStatistics();
});

function initializeTable() {
    dataTable = $('#entriesTable').DataTable({
        data: entriesData,
        columns: [
            { 
                data: 'Date',
                render: function(data) {
                    if (data) {
                        const date = new Date(data);
                        return date.toLocaleDateString();
                    }
                    return '-';
                }
            },
            {
                data: 'EmployeeName',
                render: function(data, type, row, meta) {
                    if (row.EmployeeID) {
                        // Employee has ID - show normally
                        return `<span title="${row.EmployeeID}">${data}</span>`;
                    } else if (row.PossibleEmployees && Array.isArray(row.PossibleEmployees) && row.PossibleEmployees.length > 0) {
                        // No EmployeeID but has possible employees - show dimmed name with dropdown
                        const rowId = `employee_${meta.row}`;
                        let options = '<option value="">Select Employee...</option>';
                        row.PossibleEmployees.forEach(emp => {
                            options += `<option value="${emp.EmployeeID}">${emp.EmployeeName}</option>`;
                        });
                        return `
                            <div>
                                <span class="employee-name-dimmed">${data || 'Unknown Employee'}</span><br>
                                <select class="employee-dropdown" id="${rowId}" data-row-index="${meta.row}">
                                    ${options}
                                </select>
                            </div>
                        `;
                    } else {
                        // No EmployeeID and no possible employees - show dimmed
                        return `<span class="employee-name-dimmed">${data || 'Unknown Employee'}</span>`;
                    }
                },
                defaultContent: '-',
            },
            {
                data: 'CustomerName',
                render: function(data, type, row) {
                    return row.CustomerID
                        ? `<span title="${row.CustomerID}">${data}</span>`
                        : data;
                },
                defaultContent: '-',
            },
            {
                data: 'ProjectName',
                render: function(data, type, row) {
                    return row.ProjectID
                        ? `<span title="${row.ProjectID}">${data}</span>`
                        : data;
                },
                defaultContent: '-',
            },
            {
                data: 'WorkOrderNumber',
                render: function(data, type, row, meta) {
                    if (row.WorkOrderID) {
                        // Work order already selected - show normally
                        return `<span title="${row.WorkOrderID}">${data}</span>`;
                    } else if (row.EmployeeID && row.PossibleWorkOrders && Array.isArray(row.PossibleWorkOrders) && row.PossibleWorkOrders.length > 0) {
                        // Employee selected and multiple work orders available - show dropdown
                        const rowId = `workorder_${meta.row}`;
                        let options = '<option value="">Select Work Order...</option>';
                        row.PossibleWorkOrders.forEach(wo => {
                            options += `<option value="${wo.WorkOrderID}" data-wo='${JSON.stringify(wo)}'>${wo.WorkOrderNumber} - ${wo.ProjectNumber}</option>`;
                        });
                        return `
                            <div>
                                <span class="workorder-name-dimmed">Multiple Options</span><br>
                                <select class="workorder-dropdown" id="${rowId}" data-row-index="${meta.row}">
                                    ${options}
                                </select>
                            </div>
                        `;
                    } else {
                        return data || '-';
                    }
                },
                defaultContent: '-',
            },
            { 
                data: 'CustomerReportedHours',
                render: function(data) {
                    return data ? parseFloat(data).toFixed(2) : '0.00';
                }
            },
            { 
                data: 'RateType',
                render: function(data, type, row) {
                    if (data === 'REG') {
                        return `<span class="rate-type-reg" title="Regular : ${row.RateTypeID}">REG</span>`;
                    } else if (data === 'OT') {
                        return `<span class="rate-type-ot" title="Overtime : ${row.RateTypeID}">OT</span>`;
                    } else if (data === 'DBT') {
                        return `<span class="rate-type-dt" title="Double Time : ${row.RateTypeID}">DBT</span>`;
                    } else if (data === 'TRT') {
                        return `<span class="rate-type-trt" title="Travel Time : ${row.RateTypeID}">TRT</span>`;
                    } else if (data && data.trim() !== '') {
                        return `<span class="rate-type-unknown" title="${data}">${data}</span>`;
                    }
                    return '-';
                }
            },
            {
                data: 'FileName',
                render: function(data, type, row) {
                    if (data && data.trim() !== '') {
                        // Truncate filename: first 15 chars + ... + extension
                        const truncatedName = truncateFileName(data, 15);
                        const tooltipText = row.FileID
                            ? `${data}: ${row.FileID}`
                            : data;
                        return `<span title="${tooltipText}">${truncatedName}</span>`;
                    }
                    return data || '-';
                },
                defaultContent: '-' },
            {
                data: 'ErrorMessage',
                render: function(data, type, row, meta) {
                    const isSkipped = row.SkipRecord === true || row.SkipRecord === 'true';
                    const hasError = data && data.trim() !== '';

                    if (isSkipped) {
                        // Show original status with skip icon
                        const originalStatus = hasError ? 'Error' : 'Valid';
                        const title = hasError ? `Skipped (${originalStatus}: ${data})` : 'Skipped (Valid)';
                        return `<span class="status-skipping" data-row-index="${meta.row}" title="${title}" onclick="toggleSkipStatus(${meta.row})">${originalStatus} <i class="ri-skip-forward-line align-middle"></i></span>`;
                    } else if (hasError) {
                        return `<span class="status-error" data-row-index="${meta.row}" title="${data}" onclick="toggleSkipStatus(${meta.row})">Error</span>`;
                    } else {
                        return `<span class="status-valid" data-row-index="${meta.row}" title="Valid" onclick="toggleSkipStatus(${meta.row})">Valid</span>`;
                    }
                }
            }
        ],
        pageLength: 25,
        responsive: true,
        order: [[0, 'asc']],
        dom: 'rtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ]
    });

    $('#copyButton').on('click',  () => dataTable.button('.buttons-copy').trigger());
    $('#csvButton').on('click',   () => dataTable.button('.buttons-csv').trigger());
    $('#excelButton').on('click', () => dataTable.button('.buttons-excel').trigger());
    $('#pdfButton').on('click',   () => dataTable.button('.buttons-pdf').trigger());
    $('#printButton').on('click', () => dataTable.button('.buttons-print').trigger());

    // Add event listener for employee dropdown changes
    $('#entriesTable').on('change', '.employee-dropdown', function() {
        handleEmployeeSelection(this);
    });

    // Add event listener for work order dropdown changes
    $('#entriesTable').on('change', '.workorder-dropdown', function() {
        handleWorkOrderSelection(this);
    });
}

function updateStatistics() {
    const totalHours = entriesData.reduce((sum, entry) => {
        return sum + (parseFloat(entry.CustomerReportedHours) || 0);
    }, 0);

    const totalEntries = entriesData.length;

    const uniqueEmployees = new Set(entriesData.map(entry => entry.EmployeeName).filter(name => name));
    const totalEmployees = uniqueEmployees.size;

    const uniqueCustomers = new Set(entriesData.map(entry => entry.CustomerName).filter(name => name));
    const totalCustomers = uniqueCustomers.size;

    const totalErrors = entriesData.filter(entry => entry.ErrorMessage && entry.ErrorMessage.trim() !== '').length;
    const totalSkipped = entriesData.filter(entry => entry.SkipRecord === true || entry.SkipRecord === 'true').length;

    $('#totalHours').text(totalHours.toFixed(2));
    $('#totalEntries').text(totalEntries);
    $('#totalEmployees').text(totalEmployees);
    $('#totalCustomers').text(totalCustomers);
    $('#totalErrors').text(totalErrors);
    $('#totalSkipped').text(totalSkipped);
    $('#entriesCount').text(`${totalEntries} entries (${totalSkipped} skipped)`);
    $('#totalHoursDisplay').text(totalHours.toFixed(2));
}

function setupEventListeners() {
    $('#searchInput').on('keyup', function() {
        dataTable.search(this.value).draw();
    });

    $('#sortBySelect, #sortOrderSelect').on('change', function() {
        const sortBy = $('#sortBySelect').val();
        const sortOrder = $('#sortOrderSelect').val();

        const columnIndex = getColumnIndex(sortBy);
        if (columnIndex !== -1) {
            dataTable.order([columnIndex, sortOrder]).draw();
        }
    });

    $('#groupBySelect').on('change', function() {
        const groupBy = $(this).val();
        applyGrouping(groupBy);
    });
}

function applyGrouping(groupBy) {
    // Destroy existing table
    dataTable.destroy();

    if (groupBy === '') {
        // No grouping - reinitialize normal table
        initializeTable();
    } else {
        // Apply grouping
        const columnIndex = getColumnIndex(groupBy);
        if (columnIndex !== -1) {
            dataTable = $('#entriesTable').DataTable({
                data: entriesData,
                columns: [
                    {
                        data: 'Date',
                        render: function(data) {
                            if (data) {
                                const date = new Date(data);
                                return date.toLocaleDateString();
                            }
                            return '-';
                        }
                    },
                    {
                        data: 'EmployeeName',
                        render: function(data, type, row, meta) {
                            if (row.EmployeeID) {
                                // Employee has ID - show normally
                                return `<span title="${row.EmployeeID}">${data}</span>`;
                            } else if (row.PossibleEmployees && Array.isArray(row.PossibleEmployees) && row.PossibleEmployees.length > 0) {
                                // No EmployeeID but has possible employees - show dimmed name with dropdown
                                const rowId = `employee_grouped_${meta.row}`;
                                let options = '<option value="">Select Employee...</option>';
                                row.PossibleEmployees.forEach(emp => {
                                    options += `<option value="${emp.EmployeeID}">${emp.EmployeeName}</option>`;
                                });
                                return `
                                    <div>
                                        <span class="employee-name-dimmed">${data || 'Unknown Employee'}</span><br>
                                        <select class="employee-dropdown" id="${rowId}" data-row-index="${meta.row}">
                                            ${options}
                                        </select>
                                    </div>
                                `;
                            } else {
                                // No EmployeeID and no possible employees - show dimmed
                                return `<span class="employee-name-dimmed">${data || 'Unknown Employee'}</span>`;
                            }
                        },
                        defaultContent: '-',
                    },
                    {
                        data: 'CustomerName',
                        render: function(data, type, row) {
                            return row.CustomerID
                                ? `<span title="${row.CustomerID}">${data}</span>`
                                : data;
                        },
                        defaultContent: '-',
                    },
                    {
                        data: 'ProjectName',
                        render: function(data, type, row) {
                            return row.ProjectID
                                ? `<span title="${row.ProjectID}">${data}</span>`
                                : data;
                        },
                        defaultContent: '-',
                    },
                    {
                        data: 'WorkOrderNumber',
                        render: function(data, type, row, meta) {
                            if (row.WorkOrderID) {
                                // Work order already selected - show normally
                                return `<span title="${row.WorkOrderID}">${data}</span>`;
                            } else if (row.EmployeeID && row.PossibleWorkOrders && Array.isArray(row.PossibleWorkOrders) && row.PossibleWorkOrders.length > 0) {
                                // Employee selected and multiple work orders available - show dropdown
                                const rowId = `workorder_grouped_${meta.row}`;
                                let options = '<option value="">Select Work Order...</option>';
                                row.PossibleWorkOrders.forEach(wo => {
                                    options += `<option value="${wo.WorkOrderID}" data-wo='${JSON.stringify(wo)}'>${wo.WorkOrderNumber} - ${wo.ProjectNumber}</option>`;
                                });
                                return `
                                    <div>
                                        <span class="workorder-name-dimmed">Multiple Options</span><br>
                                        <select class="workorder-dropdown" id="${rowId}" data-row-index="${meta.row}">
                                            ${options}
                                        </select>
                                    </div>
                                `;
                            } else {
                                return data || '-';
                            }
                        },
                        defaultContent: '-',
                    },
                    {
                        data: 'CustomerReportedHours',
                        render: function(data) {
                            return data ? parseFloat(data).toFixed(2) : '0.00';
                        }
                    },
                    {
                        data: 'RateType',
                        render: function(data, type, row) {
                            if (data === 'REG') {
                                return `<span class="rate-type-reg" title="Regular : ${row.RateTypeID}">REG</span>`;
                            } else if (data === 'OT') {
                                return `<span class="rate-type-ot" title="Overtime : ${row.RateTypeID}">OT</span>`;
                            } else if (data === 'DBT') {
                                return `<span class="rate-type-dt" title="Double Time : ${row.RateTypeID}">DBT</span>`;
                            } else if (data === 'TRT') {
                                return `<span class="rate-type-trt" title="Travel Time : ${row.RateTypeID}">TRT</span>`;
                            } else if (data && data.trim() !== '') {
                                return `<span class="rate-type-unknown" title="${data}">${data}</span>`;
                            }
                            return '-';
                        }
                    },
                    {
                        data: 'FileName',
                        render: function(data, type, row) {
                            if (data && data.trim() !== '') {
                                // Truncate filename: first 15 chars + ... + extension
                                const truncatedName = truncateFileName(data, 15);
                                const tooltipText = row.FileID
                                    ? `${data}; ${row.FileID}`
                                    : data;
                                return `<span title="${tooltipText}">${truncatedName}</span>`;
                            }
                            return data || '-';
                        },
                        defaultContent: '-'
                    },
                    {
                        data: 'ErrorMessage',
                        render: function(data, type, row, meta) {
                            const isSkipped = row.SkipRecord === true || row.SkipRecord === 'true';
                            const hasError = data && data.trim() !== '';

                            if (isSkipped) {
                                // Show original status with skip icon
                                const originalStatus = hasError ? 'Error' : 'Valid';
                                const title = hasError ? `Skipped (${originalStatus}: ${data})` : 'Skipped (Valid)';
                                return `<span class="status-skipping" data-row-index="${meta.row}" title="${title}" onclick="toggleSkipStatus(${meta.row})">${originalStatus} <i class="ri-skip-forward-line align-middle"></i></span>`;
                            } else if (hasError) {
                                return `<span class="status-error" data-row-index="${meta.row}" title="${data}" onclick="toggleSkipStatus(${meta.row})">Error</span>`;
                            } else {
                                return `<span class="status-valid" data-row-index="${meta.row}" title="Valid" onclick="toggleSkipStatus(${meta.row})">Valid</span>`;
                            }
                        }
                    }
                ],
                pageLength: 25,
                responsive: true,
                order: [[columnIndex, 'asc']],
                rowGroup: {
                    dataSrc: groupBy
                },
                dom: 'rtip',
                buttons: [
                    'copy', 'csv', 'excel', 'pdf', 'print'
                ]
            });

            $('#copyButton').on('click',  () => dataTable.button('.buttons-copy').trigger());
            $('#csvButton').on('click',   () => dataTable.button('.buttons-csv').trigger());
            $('#excelButton').on('click', () => dataTable.button('.buttons-excel').trigger());
            $('#pdfButton').on('click',   () => dataTable.button('.buttons-pdf').trigger());
            $('#printButton').on('click', () => dataTable.button('.buttons-print').trigger());

            // Add event listener for employee dropdown changes in grouped table
            $('#entriesTable').on('change', '.employee-dropdown', function() {
                handleEmployeeSelection(this);
            });

            // Add event listener for work order dropdown changes in grouped table
            $('#entriesTable').on('change', '.workorder-dropdown', function() {
                handleWorkOrderSelection(this);
            });
        }
    }
}

function getColumnIndex(columnName) {
    const columnMap = {
        'Date': 0,
        'EmployeeName': 1,
        'CustomerName': 2,
        'ProjectName': 3,
        'WorkOrderNumber': 4,
        'CustomerReportedHours': 5,
        'RateType': 6,
        'FileName': 7,
        'ErrorMessage': 8,
        'SkipRecord': 9
    };
    return columnMap[columnName] || -1;
}

function truncateFileName(filename, maxLength) {
    if (!filename || filename.length <= maxLength + 4) { // +4 for "..." + extension
        return filename;
    }

    // Find the last dot to identify the extension
    const lastDotIndex = filename.lastIndexOf('.');

    if (lastDotIndex === -1) {
        // No extension found, just truncate
        return filename.substring(0, maxLength) + '...';
    }

    const extension = filename.substring(lastDotIndex);
    const nameWithoutExtension = filename.substring(0, lastDotIndex);

    if (nameWithoutExtension.length <= maxLength) {
        return filename; // No need to truncate
    }

    // Truncate the name part and add ... + extension
    return nameWithoutExtension.substring(0, maxLength) + '...' + extension;
}

function handleEmployeeSelection(selectElement) {
    const selectedEmployeeID = selectElement.value;
    const selectedEmployeeName = selectElement.options[selectElement.selectedIndex].text;
    const rowIndex = parseInt(selectElement.getAttribute('data-row-index'));

    if (selectedEmployeeID && !isNaN(rowIndex)) {
        console.log(`Updating employee selection for row ${rowIndex}: ${selectedEmployeeName} (ID: ${selectedEmployeeID})`);

        // Make AJAX request to update employee selection
        fetch('/api/update_employee_selection', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                row_index: rowIndex,
                employee_id: selectedEmployeeID,
                employee_name: selectedEmployeeName
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the local data
                if (entriesData[rowIndex]) {
                    entriesData[rowIndex] = data.updated_row;
                }

                // Update the cell display to show the selected employee normally
                const cell = $(selectElement).closest('td');
                cell.html(`<span title="${selectedEmployeeID}">${selectedEmployeeName}</span>`);

                // Get the row from DataTables and redraw to reflect changes
                const row = dataTable.row($(selectElement).closest('tr'));
                row.invalidate().draw(false);

                // Update statistics
                updateStatistics();

                console.log(`Employee selection updated successfully for row ${rowIndex}`);
            } else {
                console.error('Error updating employee selection:', data.error);
                alert('Error updating employee selection: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating employee selection: ' + error.message);
        });
    }
}

function handleWorkOrderSelection(selectElement) {
    const selectedWorkOrderID = selectElement.value;
    const selectedOption = selectElement.options[selectElement.selectedIndex];
    const rowIndex = parseInt(selectElement.getAttribute('data-row-index'));

    if (selectedWorkOrderID && selectedOption.dataset.wo && !isNaN(rowIndex)) {
        try {
            const workOrder = JSON.parse(selectedOption.dataset.wo);
            console.log(`Updating work order selection for row ${rowIndex}: ${workOrder.WorkOrderNumber} (ID: ${workOrder.WorkOrderID})`);

            // Make AJAX request to update work order selection
            fetch('/api/update_work_order_selection', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    row_index: rowIndex,
                    work_order_data: workOrder
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the local data
                    if (entriesData[rowIndex]) {
                        entriesData[rowIndex] = data.updated_row;
                    }

                    // Update the cell display to show the selected work order normally
                    const cell = $(selectElement).closest('td');
                    cell.html(`<span title="${workOrder.WorkOrderID}">${workOrder.WorkOrderNumber}</span>`);

                    // Get the row from DataTables and redraw to reflect changes
                    const row = dataTable.row($(selectElement).closest('tr'));
                    row.invalidate().draw(false);

                    // Update statistics
                    updateStatistics();

                    console.log(`Work order selection updated successfully for row ${rowIndex}`);
                } else {
                    console.error('Error updating work order selection:', data.error);
                    alert('Error updating work order selection: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error updating work order selection: ' + error.message);
            });
        } catch (error) {
            console.error('Error parsing work order data:', error);
            alert('Error parsing work order data: ' + error.message);
        }
    }
}

function exportData() {
    console.log("Exporting the full data from the df...");

    // Create a temporary link to download the CSV file
    const link = document.createElement('a');
    link.href = '/export_processed_timesheet_data';
    link.download = ''; // Let the server set the filename
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function toggleSkipStatus(rowIndex) {
    console.log(`Toggling skip status for row ${rowIndex}`);

    // Make AJAX request to toggle skip status
    fetch('/api/toggle_skip_record', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            row_index: rowIndex
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the local data
            if (entriesData[rowIndex]) {
                entriesData[rowIndex].SkipRecord = data.new_skip_status;
            }

            // Redraw the table to reflect the change
            if (dataTable) {
                dataTable.row(rowIndex).invalidate().draw(false);
            }

            // Update statistics
            updateStatistics();

            console.log(`Skip status toggled for row ${rowIndex}: ${data.new_skip_status}`);
        } else {
            console.error('Error toggling skip status:', data.error);
            alert('Error toggling skip status: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error toggling skip status: ' + error.message);
    });
}
</script>
{% endblock %}
